"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import {
  Bot,
  MessageCircle,
  Quote,
  Package,
  FileText,
  Loader2,
  ChevronDown,
  ChevronUp
} from "lucide-react";

// Import actual AdMesh UI SDK components
import {
  AdMeshProductCard,
  AdMeshConversationSummary,
  AdMeshInlineRecommendation,
  AdMeshCitationUnit,
  AdMeshSidebar
} from "admesh-ui-sdk";

interface AdUnitType {
  id: string;
  name: string;
  icon: React.ReactNode;
}

interface DemoData {
  title: string;
  description: string;
  pricing: string;
  keywords: string[];
  categories: string[];
  features: string[];
  has_free_tier: boolean;
  trial_days: number;
  reason: string;
  intent_match_score: number;
  admesh_link: string;
  ad_id: string;
  product_id: string;
  url: string;
  is_ai_powered: boolean;
  offer_trust_score: number;
  brand_trust_score: number;
}

export default function InteractiveAgentDemo() {
  const [selectedAdUnit, setSelectedAdUnit] = useState<string>("product-card"); // Default to product-card
  const [isLoading, setIsLoading] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(true); // Start collapsed like brand demo

  // Demo data for popular brands
  const demoData: Record<string, DemoData> = {
    notion: {
      title: "Notion",
      description: "All-in-one workspace for notes, docs, and collaboration",
      pricing: "Free plan available, Pro from $8/month",
      keywords: ["productivity", "notes", "collaboration", "workspace", "docs"],
      categories: ["Productivity", "Collaboration", "Note-taking"],
      features: ["AI Writing Assistant", "Database Management", "Team Collaboration", "Template Library"],
      has_free_tier: true,
      trial_days: 0,
      reason: "Perfect for organizing thoughts and collaborating with AI-powered writing assistance",
      intent_match_score: 0.94,
      admesh_link: "https://notion.so?ref=admesh",
      ad_id: "ad_notion_001",
      product_id: "prod_notion",
      url: "https://notion.so",
      is_ai_powered: true,
      offer_trust_score: 0.96,
      brand_trust_score: 0.93
    },
    stripe: {
      title: "Stripe",
      description: "Complete payment infrastructure for the internet",
      pricing: "2.9% + 30¢ per transaction",
      keywords: ["payments", "fintech", "API", "e-commerce", "billing"],
      categories: ["Payments", "Fintech", "Developer Tools"],
      features: ["Global Payment Processing", "Subscription Management", "Fraud Protection", "Developer APIs"],
      has_free_tier: false,
      trial_days: 0,
      reason: "Industry-leading payment processing with comprehensive developer tools",
      intent_match_score: 0.97,
      admesh_link: "https://stripe.com?ref=admesh",
      ad_id: "ad_stripe_001",
      product_id: "prod_stripe",
      url: "https://stripe.com",
      is_ai_powered: true,
      offer_trust_score: 0.98,
      brand_trust_score: 0.97
    },
    shopify: {
      title: "Shopify",
      description: "Complete e-commerce platform for online stores",
      pricing: "Basic plan from $29/month",
      keywords: ["e-commerce", "online store", "retail", "selling", "commerce"],
      categories: ["E-commerce", "Retail", "Business"],
      features: ["Store Builder", "Payment Processing", "Inventory Management", "Marketing Tools"],
      has_free_tier: false,
      trial_days: 14,
      reason: "Complete e-commerce solution with powerful selling and marketing features",
      intent_match_score: 0.92,
      admesh_link: "https://shopify.com?ref=admesh",
      ad_id: "ad_shopify_001",
      product_id: "prod_shopify",
      url: "https://shopify.com",
      is_ai_powered: true,
      offer_trust_score: 0.94,
      brand_trust_score: 0.95
    }
  };

  // Get current recommendation based on demo data
  const getCurrentRecommendation = () => demoData.notion; // Default to Notion
  const recommendation = getCurrentRecommendation();

  const adUnitTypes: AdUnitType[] = [
    {
      id: "one-line-ad",
      name: "One Line Ad",
      icon: <MessageCircle className="w-5 h-5" />
    },
    {
      id: "product-card",
      name: "Product Card",
      icon: <Package className="w-5 h-5" />
    },
    {
      id: "conversation-summary",
      name: "Summary",
      icon: <FileText className="w-5 h-5" />
    },
    {
      id: "citation",
      name: "Citation",
      icon: <Quote className="w-5 h-5" />
    },
    {
      id: "sidebar",
      name: "Sidebar",
      icon: <Package className="w-5 h-5" />
    },
    {
      id: "floating-recommendations",
      name: "Floating",
      icon: <MessageCircle className="w-5 h-5" />
    }
  ];

  const renderAdUnitPreview = () => {
    if (!selectedAdUnit) return null;

    switch (selectedAdUnit) {
      case "one-line-ad":
        return (
          <div className="p-4 bg-white dark:bg-gray-800 rounded-lg border">
            {/*
              AdMeshInlineRecommendation: Perfect for conversational AI platforms
              - Integrates naturally within chat responses
              - Compact format for inline content
              - Easy to embed in existing text layouts
            */}
            <AdMeshInlineRecommendation
              recommendation={recommendation}
              compact={true}
              showReason={false}
              onClick={(adId, admeshLink) => window.open(admeshLink, '_blank')}
            />
          </div>
        );

      case "product-card":
        return (
          <div className="p-4 bg-white dark:bg-gray-800 rounded-lg border">
            {/*
              AdMeshProductCard: Perfect for AI platforms with rich content areas
              - Shows detailed product information
              - Includes match scores and badges
              - Great for recommendation feeds
            */}
            <AdMeshProductCard
              recommendation={recommendation}
              showMatchScore={true}
              showBadges={true}
              maxKeywords={3}
              onClick={(adId, admeshLink) => window.open(admeshLink, '_blank')}
            />
          </div>
        );

      case "conversation-summary":
        return (
          <div className="p-4 bg-white dark:bg-gray-800 rounded-lg border">
            {/*
              AdMeshConversationSummary: Perfect for AI chat platforms
              - Summarizes conversation with recommendations
              - Shows multiple products in context
              - Great for end-of-conversation summaries
            */}
            <AdMeshConversationSummary
              recommendations={[recommendation, demoData.stripe, demoData.shopify]}
              conversationSummary={`Based on our conversation about ${recommendation.categories[0].toLowerCase()} tools, here are the top recommendations for your workflow.`}
              showTopRecommendations={3}
              onRecommendationClick={(adId, admeshLink) => window.open(admeshLink, '_blank')}
            />
          </div>
        );

      case "citation":
        return (
          <div className="p-4 bg-white dark:bg-gray-800 rounded-lg border">
            {/*
              AdMeshCitationUnit: Perfect for educational/research AI platforms
              - Integrates naturally within content containers
              - Shows academic-style numbered citations
              - Easy to embed in existing text layouts
            */}
            <AdMeshCitationUnit
              recommendations={[recommendation]}
              conversationText={`Based on recent studies, the most effective ${recommendation.categories[0].toLowerCase()} tools include several key features that enhance team productivity and collaboration.`}
              showCitationList={true}
              citationStyle="numbered"
              onRecommendationClick={(adId, admeshLink) => window.open(admeshLink, '_blank')}
              onCitationHover={(rec) => console.log('Citation hovered:', rec.title)}
            />
          </div>
        );

      case "sidebar":
        return (
          <div className="bg-white dark:bg-gray-800 rounded-lg border relative min-h-[400px] flex overflow-hidden">
            <div className="flex-1 p-4">
              <div className="text-sm text-gray-700 dark:text-gray-300 mb-4">
                Your AI platform content appears here. The sidebar provides persistent access to recommendations.
              </div>
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-1/2"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-2/3"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-5/6"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-1/3"></div>
              </div>
            </div>

            {/*
              AdMeshSidebar with containerMode for demo integration
              - Uses the actual SDK component with containerMode=true
              - Perfect for AI platform dashboards and interfaces
              - Shows real sidebar functionality within preview container
            */}
            <AdMeshSidebar
              recommendations={[recommendation, demoData.stripe]}
              config={{
                position: 'right',
                size: 'md',
                displayMode: 'recommendations',
                collapsible: false,
                showHeader: true,
                showSearch: false,
                maxRecommendations: 2
              }}
              title="AI Recommendations"
              isOpen={true}
              containerMode={true}
              onRecommendationClick={(adId, admeshLink) => window.open(admeshLink, '_blank')}
              className="border-l-0"
            />
          </div>
        );

      case "floating-recommendations":
        return (
          <div className="p-4 bg-white dark:bg-gray-800 rounded-lg border relative min-h-[250px]">
            <div className="text-sm text-gray-700 dark:text-gray-300 mb-4">
              Your AI platform interface appears here. The floating recommendation widget appears contextually based on user behavior.
            </div>
            <div className="space-y-2 mb-8">
              <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-1/2"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-2/3"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-5/6"></div>
            </div>

            {/*
              Custom floating widget using AdMeshInlineRecommendation
              - Shows how to create floating overlays with SDK components
              - Perfect for contextual recommendations in AI platforms
              - Keeps the widget contained within the preview
            */}
            <div className="absolute bottom-4 right-4 w-72 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg">
              <div className="p-3 border-b border-gray-200 dark:border-gray-600">
                <div className="flex items-center justify-between">
                  <div className="text-sm font-medium text-gray-900 dark:text-white">AI Recommendation</div>
                  <button className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>
              <div className="p-3">
                <AdMeshInlineRecommendation
                  recommendation={recommendation}
                  compact={true}
                  showReason={true}
                  onClick={(adId, admeshLink) => window.open(admeshLink, '_blank')}
                />
                <div className="mt-3 text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  Powered by AdMesh
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto px-4 sm:px-0">
      <Card className="border border-gray-200 dark:border-gray-700 shadow-lg bg-white dark:bg-gray-800">
        <CardHeader className="text-center pb-4">
          <CardTitle className="flex items-center justify-center gap-2 text-lg text-gray-900 dark:text-white">
            <Bot className="w-5 h-5 text-purple-600 dark:text-purple-400" />
            Interactive Agent Demo
          </CardTitle>
          <p className="text-gray-600 dark:text-gray-300 text-sm">
            Select an ad unit type to see live previews using actual AdMesh UI components
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Ad Unit Type Selector */}
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-900 dark:text-white">
                Choose Ad Unit Type
              </label>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Select an ad unit to see a live preview using actual AdMesh UI components
              </p>
            </div>

            {/* Collapsible Ad Unit Selection */}
            <div className="space-y-2">
              <Button
                variant="outline"
                onClick={() => setIsCollapsed(!isCollapsed)}
                className="w-full flex items-center justify-between p-3 text-left"
              >
                <span className="text-sm">
                  {selectedAdUnit
                    ? `Selected: ${adUnitTypes.find(unit => unit.id === selectedAdUnit)?.name}`
                    : 'Click to choose ad unit type'
                  }
                </span>
                {isCollapsed ? <ChevronDown className="w-4 h-4" /> : <ChevronUp className="w-4 h-4" />}
              </Button>

              {!isCollapsed && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  className="grid grid-cols-2 sm:grid-cols-3 gap-2 p-3 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800/50"
                >
                  {adUnitTypes.map((unit) => (
                    <Button
                      key={unit.id}
                      variant={selectedAdUnit === unit.id ? "default" : "outline"}
                      onClick={() => {
                        setSelectedAdUnit(unit.id);
                        setIsCollapsed(true);
                      }}
                      className={`h-auto p-2 flex flex-col items-center gap-1 text-center hover:shadow-sm transition-all ${
                        selectedAdUnit === unit.id
                          ? "bg-purple-600 hover:bg-purple-700 text-white border-purple-600"
                          : "hover:bg-gray-50 dark:hover:bg-gray-700"
                      }`}
                      size="sm"
                    >
                      <div className={`w-6 h-6 rounded-md flex items-center justify-center flex-shrink-0 ${
                        selectedAdUnit === unit.id
                          ? "bg-purple-500 text-white"
                          : "bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400"
                      }`}>
                        {unit.icon}
                      </div>
                      <div className="text-xs font-medium truncate w-full">
                        {unit.name}
                      </div>
                    </Button>
                  ))}
                </motion.div>
              )}
            </div>
          </div>

          {/* Live Preview */}
          {selectedAdUnit && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-2 mt-4"
            >
              <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                Live Preview - {adUnitTypes.find(unit => unit.id === selectedAdUnit)?.name}
              </h4>
              <div className="border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-900/50">
                {renderAdUnitPreview()}
              </div>
            </motion.div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
