{"name": "publint", "version": "0.2.12", "description": "Lint packaging errors", "type": "module", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "bin": "./lib/cli.js", "types": "./index.d.ts", "exports": {".": {"types": "./index.d.ts", "browser": "./lib/browser.js", "node": "./lib/node.js", "default": "./src/index.js"}, "./utils": {"types": "./utils.d.ts", "node": "./lib/utils-node.js", "default": "./lib/utils.js"}}, "engines": {"node": ">=16"}, "files": ["lib", "src", "*.d.ts"], "funding": "https://bjornlu.com/sponsor", "homepage": "https://publint.dev", "repository": {"type": "git", "url": "git+https://github.com/bluwy/publint.git", "directory": "pkg"}, "bugs": {"url": "https://github.com/bluwy/publint/issues"}, "keywords": ["publish", "lint"], "dependencies": {"npm-packlist": "^5.1.3", "picocolors": "^1.1.1", "sade": "^1.8.1"}, "devDependencies": {"@types/npm-packlist": "^3.0.0", "uvu": "^0.5.6"}, "scripts": {"cli": "node ./lib/cli.js", "test": "uvu tests -i fixtures"}}