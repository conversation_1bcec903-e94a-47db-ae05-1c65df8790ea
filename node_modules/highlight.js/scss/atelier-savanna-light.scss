/* Base16 Atelier Savanna Light - Theme */
/* by <PERSON> (http://atelierbram.github.io/syntax-highlighting/atelier-schemes/savanna) */
/* Original Base16 color scheme by <PERSON> (https://github.com/chris<PERSON><PERSON><PERSON>/base16) */

/* Atelier-Savanna Comment */
.hljs-comment,
.hljs-quote {
  color: #5f6d64;
}

/* Atelier-Savanna Red */
.hljs-variable,
.hljs-template-variable,
.hljs-attribute,
.hljs-tag,
.hljs-name,
.hljs-regexp,
.hljs-link,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class {
  color: #b16139;
}

/* Atelier-Savanna Orange */
.hljs-number,
.hljs-meta,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params {
  color: #9f713c;
}

/* Atelier-Savanna Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet {
  color: #489963;
}

/* Atelier-Savanna Blue */
.hljs-title,
.hljs-section {
  color: #478c90;
}

/* Atelier-Savanna Purple */
.hljs-keyword,
.hljs-selector-tag {
  color: #55859b;
}

.hljs-deletion,
.hljs-addition {
  color: #171c19;
  display: inline-block;
  width: 100%;
}

.hljs-deletion {
  background-color: #b16139;
}

.hljs-addition {
  background-color: #489963;
}

.hljs {
  display: block;
  overflow-x: auto;
  background: #ecf4ee;
  color: #526057;
  padding: 0.5em;
}

.hljs-emphasis {
  font-style: italic;
}

.hljs-strong {
  font-weight: bold;
}
