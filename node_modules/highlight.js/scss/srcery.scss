/*
Description: Srcery dark color scheme for highlight.js
Author: <PERSON> <<EMAIL>>
Website: https://srcery-colors.github.io/
Date: 2020-04-06
*/

.hljs {
  display: block;
  overflow-x: auto;
  padding: 0.5em;
  background: #1C1B19;
  color: #FCE8C3;
}

.hljs-strong,
.hljs-emphasis {
  color: #918175;
}

.hljs-bullet,
.hljs-quote,
.hljs-link,
.hljs-number,
.hljs-regexp,
.hljs-literal {
  color: #FF5C8F;
}

.hljs-code,
.hljs-selector-class {
  color: #68A8E4
}

.hljs-emphasis {
  font-style: italic;
}

.hljs-keyword,
.hljs-selector-tag,
.hljs-section,
.hljs-attribute,
.hljs-variable {
  color: #EF2F27;
}

.hljs-name,
.hljs-title {
  color: #FBB829;
}

.hljs-type,
.hljs-params {
  color: #0AAEB3;
}

.hljs-string {
  color: #98BC37;
}

.hljs-subst,
.hljs-built_in,
.hljs-builtin-name,
.hljs-symbol,
.hljs-selector-id,
.hljs-selector-attr,
.hljs-selector-pseudo,
.hljs-template-tag,
.hljs-template-variable,
.hljs-addition {
  color: #C07ABE;
}

.hljs-comment,
.hljs-deletion,
.hljs-meta {
  color: #918175;
}
