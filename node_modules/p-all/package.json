{"name": "p-all", "version": "3.0.0", "description": "Run promise-returning & async functions concurrently with optional limited concurrency", "license": "MIT", "repository": "sindresorhus/p-all", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["promise", "all", "function", "func", "fn", "limited", "limit", "control", "rate", "collection", "iterable", "iterator", "fulfilled", "async", "await", "promises", "concurrent", "concurrently", "concurrency", "parallel", "bluebird"], "dependencies": {"p-map": "^4.0.0"}, "devDependencies": {"ava": "^1.4.1", "delay": "^4.1.0", "tsd": "^0.11.0", "xo": "^0.28.0"}}