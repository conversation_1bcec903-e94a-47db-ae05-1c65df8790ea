{"name": "superstruct", "description": "A simple and composable way to validate data in JavaScript (and TypeScript).", "version": "1.0.4", "license": "MIT", "repository": "git://github.com/ianstormtaylor/superstruct.git", "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "sideEffects": false, "files": ["dist"], "publishConfig": {"registry": "https://registry.npmjs.org"}, "engines": {"node": ">=14.0.0"}, "devDependencies": {"@babel/cli": "^7.6.3", "@babel/core": "^7.6.3", "@babel/plugin-transform-modules-commonjs": "^7.12.1", "@babel/preset-env": "^7.20.2", "@babel/preset-typescript": "^7.6.0", "@babel/register": "^7.6.2", "@rollup/plugin-typescript": "^9.0.2", "@types/expect": "^24.3.0", "@types/lodash": "^4.14.144", "@types/mocha": "^10.0.0", "@types/node": "^18.7.14", "@typescript-eslint/eslint-plugin": "^5.43.0", "@typescript-eslint/parser": "^5.43.0", "babel-eslint": "^10.0.3", "eslint": "^7.14.0", "eslint-config-prettier": "^7.2.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^4.0.0", "is-email": "^1.0.0", "is-url": "^1.2.4", "is-uuid": "^1.0.2", "lodash": "^4.17.15", "mocha": "^10.0.0", "np": "^7.6.2", "prettier": "^2.0.5", "rollup": "^3.3.0", "typescript": "^4.8.3"}, "scripts": {"build": "rm -rf ./{dist} && rollup --config ./rollup.config.js", "clean": "rm -rf ./{dist,node_modules}", "fix": "npm run fix:eslint && npm run fix:prettier", "fix:eslint": "npm run lint:eslint --fix", "fix:prettier": "npm run lint:prettier --write", "lint": "npm run lint:eslint && npm run lint:prettier", "lint:eslint": "eslint '{src,test}/*.{js,ts}'", "lint:prettier": "prettier --list-different '**/*.{js,json,ts}'", "release": "npm run build && npm run lint && np", "test": "npm run build && npm run test:types && npm run test:mocha", "test:mocha": "mocha --require ./test/register.cjs --require source-map-support/register ./test/index.ts", "test:types": "tsc --noEmit && tsc --project ./test/tsconfig.json --noEmit", "watch": "npm run build -- --watch"}, "keywords": ["api", "array", "assert", "cast", "check", "checker", "collection", "data", "error", "express", "hapi", "interface", "invalid", "joi", "json", "list", "model", "object", "orm", "scalar", "schema", "struct", "throw", "type", "types", "valid", "validate", "validation", "validator"]}