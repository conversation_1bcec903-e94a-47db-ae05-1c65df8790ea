"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createRewriteImportTransformer = createRewriteImportTransformer;
const tslib_1 = require("tslib");
const path_1 = require("path");
const utils_1 = require("../utils");
const assert_1 = tslib_1.__importDefault(require("assert"));
const typescript_1 = require("typescript");
const JS_EXT = ".js";
const JSON_EXT = ".json";
function isRelativePath(path) {
    return path.startsWith("./") || path.startsWith("../");
}
function positionIsSynthesized(pos) {
    // This is a fast way of testing the following conditions:
    //  pos === undefined || pos === null || isNaN(pos) || pos < 0;
    return !(pos >= 0);
}
function nodeIsSynthesized(range) {
    return positionIsSynthesized(range.pos) || positionIsSynthesized(range.end);
}
function createRewriteImportTransformer(options) {
    const { sys, factory, isStringLiteral, isImportDeclaration, isCallExpression, SyntaxKind, visitNode, visitEachChild, isIdentifier, isExportDeclaration, isVariableStatement, isSourceFile, } = options.ts;
    function isDirectory(sourceFile, path) {
        const sourcePath = sourceFile.fileName;
        const fullPath = (0, path_1.resolve)((0, path_1.dirname)(sourcePath), path);
        return sys.directoryExists(fullPath);
    }
    function fileExists(sourceFile, path) {
        const sourcePath = sourceFile.fileName;
        const fullPath = (0, path_1.resolve)((0, path_1.dirname)(sourcePath), path);
        return sys.fileExists(fullPath);
    }
    function updateModuleSpecifier(ctx, sourceFile, node) {
        if (!isStringLiteral(node) || !isRelativePath(node.text))
            return node;
        const ext = (0, path_1.extname)(node.text);
        if (ext === JSON_EXT && ctx.getCompilerOptions().resolveJsonModule) {
            return node;
        }
        const base = ext === JS_EXT ? (0, utils_1.trimSuffix)(node.text, JS_EXT) : node.text;
        if (!(fileExists(sourceFile, `${base}.ts`) ||
            fileExists(sourceFile, `${base}.js`)) &&
            isDirectory(sourceFile, node.text)) {
            return factory.createStringLiteral(`${node.text}/index${options.extname}`);
        }
        return factory.createStringLiteral(`${base}${options.extname}`);
    }
    const tslibRequires = new WeakSet();
    return (ctx) => {
        const resolvedShareHelpers = options.getResolvedShareHelpers();
        let sourceFile;
        function getRelativeImport(mod) {
            const r = (0, path_1.relative)((0, path_1.dirname)(sourceFile.fileName), mod);
            return /^\.?\.?\//.test(r) ? r : "./" + r;
        }
        const visitor = (node) => {
            if (resolvedShareHelpers && isSourceFile(node)) {
                for (const helper of node.emitNode?.helpers ?? []) {
                    if (!helper.scoped) {
                        options.helpersNeeded.add(helper.importName);
                    }
                }
            }
            if (resolvedShareHelpers) {
                if (isImportDeclaration(node) &&
                    nodeIsSynthesized(node) &&
                    isStringLiteral(node.moduleSpecifier) &&
                    node.moduleSpecifier.text === "tslib") {
                    return factory.createImportDeclaration(node.modifiers, node.importClause, factory.createStringLiteral(getRelativeImport(resolvedShareHelpers)), node.attributes);
                }
                if (isVariableStatement(node)) {
                    const requireCall = node.declarationList.declarations[0]?.initializer;
                    let original;
                    if (requireCall &&
                        isCallExpression(requireCall) &&
                        isIdentifier(requireCall.expression) &&
                        requireCall.expression.escapedText === "require" &&
                        "original" in node &&
                        typeof node.original === "object" &&
                        node.original !== null &&
                        ((original = node.original),
                            nodeIsSynthesized(original)) &&
                        (isImportDeclaration(original)
                            ? isStringLiteral(original.moduleSpecifier) &&
                                original.moduleSpecifier.text === "tslib"
                            : (0, typescript_1.isImportEqualsDeclaration)(original) &&
                                (0, typescript_1.isExternalModuleReference)(original.moduleReference) &&
                                isStringLiteral(original.moduleReference.expression) &&
                                original.moduleReference.expression.text === "tslib")) {
                        tslibRequires.add(requireCall);
                    }
                }
            }
            // ESM import
            if (isImportDeclaration(node)) {
                return factory.createImportDeclaration(node.modifiers, node.importClause, updateModuleSpecifier(ctx, sourceFile, node.moduleSpecifier), node.attributes);
            }
            // ESM export
            if (isExportDeclaration(node)) {
                if (!node.moduleSpecifier)
                    return node;
                return factory.createExportDeclaration(node.modifiers, node.isTypeOnly, node.exportClause, updateModuleSpecifier(ctx, sourceFile, node.moduleSpecifier), node.attributes);
            }
            // ESM dynamic import
            if (isCallExpression(node) &&
                node.expression.kind === SyntaxKind.ImportKeyword) {
                const [firstArg, ...restArg] = node.arguments;
                if (!firstArg)
                    return node;
                return factory.createCallExpression(node.expression, node.typeArguments, [updateModuleSpecifier(ctx, sourceFile, firstArg), ...restArg]);
            }
            // CommonJS require
            if (isCallExpression(node) &&
                isIdentifier(node.expression) &&
                node.expression.escapedText === "require") {
                const [firstArg, ...restArgs] = node.arguments;
                if (!firstArg)
                    return node;
                return factory.createCallExpression(node.expression, node.typeArguments, [
                    resolvedShareHelpers && tslibRequires.has(node)
                        ? factory.createStringLiteral(getRelativeImport(resolvedShareHelpers))
                        : updateModuleSpecifier(ctx, sourceFile, firstArg),
                    ...restArgs,
                ]);
            }
            return visitEachChild(node, visitor, ctx);
        };
        return (file) => {
            if (options.ts.isSourceFile(file)) {
                sourceFile = file;
                return visitNode(file, visitor);
            }
            else if (options.ts.isBundle(file)) {
                return ctx.factory.createBundle(file.sourceFiles.map((file) => {
                    sourceFile = file;
                    return visitNode(file, visitor);
                }));
            }
            else {
                (0, assert_1.default)(false);
            }
        };
    };
}
//# sourceMappingURL=rewriteImport.js.map