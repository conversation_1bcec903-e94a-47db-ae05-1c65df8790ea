{"version": 3, "file": "rewriteImport.js", "sourceRoot": "", "sources": ["../../src/transformers/rewriteImport.ts"], "names": [], "mappings": ";;AAkCA,wEA2MC;;AA7OD,+BAA2D;AAE3D,oCAAsC;AACtC,4DAA4B;AAC5B,2CAGoB;AAEpB,MAAM,MAAM,GAAG,KAAK,CAAC;AACrB,MAAM,QAAQ,GAAG,OAAO,CAAC;AAEzB,SAAS,cAAc,CAAC,IAAY;IAClC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AACzD,CAAC;AAUD,SAAS,qBAAqB,CAAC,GAAW;IACxC,0DAA0D;IAC1D,+DAA+D;IAC/D,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AACrB,CAAC;AAED,SAAS,iBAAiB,CAAC,KAAmB;IAC5C,OAAO,qBAAqB,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,qBAAqB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC9E,CAAC;AAED,SAAgB,8BAA8B,CAE5C,OAAwC;IACxC,MAAM,EACJ,GAAG,EACH,OAAO,EACP,eAAe,EACf,mBAAmB,EACnB,gBAAgB,EAChB,UAAU,EACV,SAAS,EACT,cAAc,EACd,YAAY,EACZ,mBAAmB,EACnB,mBAAmB,EACnB,YAAY,GACb,GAAG,OAAO,CAAC,EAAE,CAAC;IAEf,SAAS,WAAW,CAAC,UAAyB,EAAE,IAAY;QAC1D,MAAM,UAAU,GAAG,UAAU,CAAC,QAAQ,CAAC;QACvC,MAAM,QAAQ,GAAG,IAAA,cAAO,EAAC,IAAA,cAAO,EAAC,UAAU,CAAC,EAAE,IAAI,CAAC,CAAC;QAEpD,OAAO,GAAG,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;IACvC,CAAC;IAED,SAAS,UAAU,CAAC,UAAyB,EAAE,IAAY;QACzD,MAAM,UAAU,GAAG,UAAU,CAAC,QAAQ,CAAC;QACvC,MAAM,QAAQ,GAAG,IAAA,cAAO,EAAC,IAAA,cAAO,EAAC,UAAU,CAAC,EAAE,IAAI,CAAC,CAAC;QAEpD,OAAO,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IAClC,CAAC;IAED,SAAS,qBAAqB,CAC5B,GAA6B,EAC7B,UAAyB,EACzB,IAAmB;QAEnB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;YAAE,OAAO,IAAI,CAAC;QAEtE,MAAM,GAAG,GAAG,IAAA,cAAO,EAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE/B,IAAI,GAAG,KAAK,QAAQ,IAAI,GAAG,CAAC,kBAAkB,EAAE,CAAC,iBAAiB,EAAE,CAAC;YACnE,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,IAAI,GAAG,GAAG,KAAK,MAAM,CAAC,CAAC,CAAC,IAAA,kBAAU,EAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;QAExE,IACE,CAAC,CACC,UAAU,CAAC,UAAU,EAAE,GAAG,IAAI,KAAK,CAAC;YACpC,UAAU,CAAC,UAAU,EAAE,GAAG,IAAI,KAAK,CAAC,CACrC;YACD,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,EAClC,CAAC;YACD,OAAO,OAAO,CAAC,mBAAmB,CAChC,GAAG,IAAI,CAAC,IAAI,SAAS,OAAO,CAAC,OAAO,EAAE,CACvC,CAAC;QACJ,CAAC;QAED,OAAO,OAAO,CAAC,mBAAmB,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;IAClE,CAAC;IAED,MAAM,aAAa,GAAG,IAAI,OAAO,EAAW,CAAC;IAE7C,OAAO,CAAC,GAAG,EAAE,EAAE;QACb,MAAM,oBAAoB,GAAG,OAAO,CAAC,uBAAuB,EAAE,CAAC;QAC/D,IAAI,UAAyB,CAAC;QAE9B,SAAS,iBAAiB,CAAC,GAAW;YACpC,MAAM,CAAC,GAAG,IAAA,eAAQ,EAAC,IAAA,cAAO,EAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,CAAC;YACtD,OAAO,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC;QAC5C,CAAC;QAED,MAAM,OAAO,GAAe,CAAC,IAAI,EAAE,EAAE;YACnC,IAAI,oBAAoB,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC/C,KAAK,MAAM,MAAM,IAAM,IAAY,CAAC,QAAQ,EAAE,OAAiB,IAAI,EAAE,EAAE,CAAC;oBACtE,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;wBACnB,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;oBAC/C,CAAC;gBACH,CAAC;YACH,CAAC;YAED,IAAI,oBAAoB,EAAE,CAAC;gBACzB,IACE,mBAAmB,CAAC,IAAI,CAAC;oBACzB,iBAAiB,CAAC,IAAI,CAAC;oBACvB,eAAe,CAAC,IAAI,CAAC,eAAe,CAAC;oBACrC,IAAI,CAAC,eAAe,CAAC,IAAI,KAAK,OAAO,EACrC,CAAC;oBACD,OAAO,OAAO,CAAC,uBAAuB,CACpC,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,YAAY,EACjB,OAAO,CAAC,mBAAmB,CACzB,iBAAiB,CAAC,oBAAoB,CAAC,CACxC,EACD,IAAI,CAAC,UAAU,CAChB,CAAC;gBACJ,CAAC;gBACD,IAAI,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC9B,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC;oBACtE,IAAI,QAAiB,CAAC;oBACtB,IACE,WAAW;wBACX,gBAAgB,CAAC,WAAW,CAAC;wBAC7B,YAAY,CAAC,WAAW,CAAC,UAAU,CAAC;wBACpC,WAAW,CAAC,UAAU,CAAC,WAAW,KAAK,SAAS;wBAChD,UAAU,IAAI,IAAI;wBAClB,OAAO,IAAI,CAAC,QAAQ,KAAK,QAAQ;wBACjC,IAAI,CAAC,QAAQ,KAAK,IAAI;wBACtB,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAmB,CAAC;4BACtC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;wBAC5B,CAAC,mBAAmB,CAAC,QAAQ,CAAC;4BAC5B,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC,eAAe,CAAC;gCACzC,QAAQ,CAAC,eAAe,CAAC,IAAI,KAAK,OAAO;4BAC3C,CAAC,CAAC,IAAA,sCAAyB,EAAC,QAAQ,CAAC;gCACnC,IAAA,sCAAyB,EAAC,QAAQ,CAAC,eAAe,CAAC;gCACnD,eAAe,CAAC,QAAQ,CAAC,eAAe,CAAC,UAAU,CAAC;gCACpD,QAAQ,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,KAAK,OAAO,CAAC,EACzD,CAAC;wBACD,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;oBACjC,CAAC;gBACH,CAAC;YACH,CAAC;YACD,aAAa;YACb,IAAI,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC9B,OAAO,OAAO,CAAC,uBAAuB,CACpC,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,YAAY,EACjB,qBAAqB,CAAC,GAAG,EAAE,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,EAC5D,IAAI,CAAC,UAAU,CAChB,CAAC;YACJ,CAAC;YAED,aAAa;YACb,IAAI,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC9B,IAAI,CAAC,IAAI,CAAC,eAAe;oBAAE,OAAO,IAAI,CAAC;gBAEvC,OAAO,OAAO,CAAC,uBAAuB,CACpC,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,YAAY,EACjB,qBAAqB,CAAC,GAAG,EAAE,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,EAC5D,IAAI,CAAC,UAAU,CAChB,CAAC;YACJ,CAAC;YAED,qBAAqB;YACrB,IACE,gBAAgB,CAAC,IAAI,CAAC;gBACtB,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,UAAU,CAAC,aAAa,EACjD,CAAC;gBACD,MAAM,CAAC,QAAQ,EAAE,GAAG,OAAO,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;gBAC9C,IAAI,CAAC,QAAQ;oBAAE,OAAO,IAAI,CAAC;gBAE3B,OAAO,OAAO,CAAC,oBAAoB,CACjC,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,aAAa,EAClB,CAAC,qBAAqB,CAAC,GAAG,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAE,GAAG,OAAO,CAAC,CAC/D,CAAC;YACJ,CAAC;YAED,mBAAmB;YACnB,IACE,gBAAgB,CAAC,IAAI,CAAC;gBACtB,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC;gBAC7B,IAAI,CAAC,UAAU,CAAC,WAAW,KAAK,SAAS,EACzC,CAAC;gBACD,MAAM,CAAC,QAAQ,EAAE,GAAG,QAAQ,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;gBAC/C,IAAI,CAAC,QAAQ;oBAAE,OAAO,IAAI,CAAC;gBAE3B,OAAO,OAAO,CAAC,oBAAoB,CACjC,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,aAAa,EAClB;oBACE,oBAAoB,IAAI,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC;wBAC7C,CAAC,CAAC,OAAO,CAAC,mBAAmB,CACzB,iBAAiB,CAAC,oBAAoB,CAAC,CACxC;wBACH,CAAC,CAAC,qBAAqB,CAAC,GAAG,EAAE,UAAU,EAAE,QAAQ,CAAC;oBACpD,GAAG,QAAQ;iBACZ,CACF,CAAC;YACJ,CAAC;YAED,OAAO,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;QAC5C,CAAC,CAAC;QAEF,OAAO,CAAC,IAAI,EAAE,EAAE;YACd,IAAI,OAAO,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;gBAClC,UAAU,GAAG,IAAI,CAAC;gBAClB,OAAO,SAAS,CAAC,IAAI,EAAE,OAAO,CAAQ,CAAC;YACzC,CAAC;iBAAM,IAAI,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBACrC,OAAO,GAAG,CAAC,OAAO,CAAC,YAAY,CAC7B,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;oBAC5B,UAAU,GAAG,IAAI,CAAC;oBAClB,OAAO,SAAS,CAAC,IAAI,EAAE,OAAO,CAAkB,CAAC;gBACnD,CAAC,CAAC,CACI,CAAC;YACX,CAAC;iBAAM,CAAC;gBACN,IAAA,gBAAM,EAAC,KAAK,CAAC,CAAC;YAChB,CAAC;QACH,CAAC,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC"}