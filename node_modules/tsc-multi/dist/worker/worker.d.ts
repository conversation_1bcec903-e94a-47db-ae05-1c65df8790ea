import type ts from "typescript";
import { WorkerOptions } from "./types";
export declare class Worker {
    private readonly data;
    private readonly ts;
    private readonly system;
    private readonly reporter;
    constructor(data: WorkerOptions, system?: ts.System);
    run(): number;
    private getJSPath;
    private getJSMapPath;
    private getDTSPath;
    private getDTSMapPath;
    private rewritePath;
    private rewriteSourceMappingURL;
    private rewriteSourceMap;
    private rewriteDTSMappingURL;
    private rewriteDTSMap;
    private createSystem;
    private createBuilder;
    private patchSolutionBuilderHost;
    private writeHelpers;
    private transpile;
    private transpileProject;
}
