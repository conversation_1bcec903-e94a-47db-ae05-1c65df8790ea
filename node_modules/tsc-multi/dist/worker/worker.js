"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Worker = void 0;
const tslib_1 = require("tslib");
const debug_1 = tslib_1.__importDefault(require("./debug"));
const report_1 = require("../report");
const utils_1 = require("../utils");
const rewriteImport_1 = require("../transformers/rewriteImport");
const path_1 = require("path");
const assert_1 = tslib_1.__importDefault(require("assert"));
const helpers_1 = require("../helpers");
const JS_EXT = ".js";
const MAP_EXT = ".map";
const JS_MAP_EXT = `${JS_EXT}${MAP_EXT}`;
const DTS_EXT = ".d.ts";
const DTS_MAP_EXT = `${DTS_EXT}${MAP_EXT}`;
const extnameDeclMap = {
    ".js": ".d.ts",
    ".mjs": ".d.mts",
    ".cjs": ".d.cts",
};
function loadCompiler(cwd, name = "typescript") {
    const path = require.resolve(name, { paths: [cwd, __dirname] });
    return require(path);
}
class Worker {
    constructor(data, system) {
        this.data = data;
        this.ts = loadCompiler(data.cwd, data.compiler);
        this.system = this.createSystem(system || this.ts.sys);
        this.reporter = (0, report_1.createReporter)({
            cwd: data.cwd,
            system: this.system,
            formatDiagnostics: this.ts.formatDiagnosticsWithColorAndContext,
            output: process.stderr,
            prefix: data.reportPrefix,
        });
    }
    run() {
        if (this.data.transpileOnly) {
            this.transpile();
            return 0;
        }
        const builder = this.createBuilder();
        if (this.data.clean) {
            return builder.clean();
        }
        return builder.build();
    }
    getJSPath(path) {
        if (!this.data.extname)
            return path;
        return (0, utils_1.trimSuffix)(path, JS_EXT) + this.data.extname;
    }
    getJSMapPath(path) {
        if (!this.data.extname)
            return path;
        return (0, utils_1.trimSuffix)(path, JS_MAP_EXT) + this.data.extname + MAP_EXT;
    }
    getDTSPath(path) {
        if (!this.data.extname)
            return path;
        return (0, utils_1.trimSuffix)(path, DTS_EXT) + extnameDeclMap[this.data.extname];
    }
    getDTSMapPath(path) {
        if (!this.data.extname)
            return path;
        return ((0, utils_1.trimSuffix)(path, DTS_MAP_EXT) +
            extnameDeclMap[this.data.extname] +
            MAP_EXT);
    }
    rewritePath(path) {
        if (path.endsWith(JS_EXT)) {
            return this.getJSPath(path);
        }
        if (path.endsWith(JS_MAP_EXT)) {
            return this.getJSMapPath(path);
        }
        if (path.endsWith(DTS_EXT)) {
            return this.getDTSPath(path);
        }
        if (path.endsWith(DTS_MAP_EXT)) {
            return this.getDTSMapPath(path);
        }
        return path;
    }
    rewriteSourceMappingURL(data) {
        return data.replace(/\/\/# sourceMappingURL=(.+)/g, (_, path) => `//# sourceMappingURL=${this.getJSMapPath(path)}`);
    }
    rewriteSourceMap(data) {
        const json = JSON.parse(data);
        json.file = this.getJSPath(json.file);
        return JSON.stringify(json);
    }
    rewriteDTSMappingURL(data) {
        return data.replace(/\/\/# sourceMappingURL=(.+)/g, (_, path) => `//# sourceMappingURL=${this.getDTSMapPath(path)}`);
    }
    rewriteDTSMap(data) {
        const json = JSON.parse(data);
        json.file = this.getDTSPath(json.file);
        return JSON.stringify(json);
    }
    createSystem(sys) {
        const getReadPaths = (path) => {
            const inNodeModules = path.includes("/node_modules/");
            const paths = [inNodeModules ? path : this.rewritePath(path)];
            // Source files may be .js files when `allowJs` is enabled. When a .js
            // file with rewritten path doesn't exist, retry again without rewriting
            // the path.
            if (!inNodeModules && (path.endsWith(JS_EXT) || path.endsWith(DTS_EXT))) {
                paths.push(path);
            }
            return paths;
        };
        return {
            ...sys,
            fileExists: (inputPath) => {
                return getReadPaths(inputPath).reduce((result, path) => result || sys.fileExists(path), false);
            },
            readFile: (inputPath, encoding) => {
                return (getReadPaths(inputPath).reduce((result, path) => result ?? sys.readFile(path, encoding), null) ?? undefined);
            },
            writeFile: (path, data, writeByteOrderMark) => {
                const newPath = this.rewritePath(path);
                const newData = (() => {
                    if (path.endsWith(JS_EXT)) {
                        return this.rewriteSourceMappingURL(data);
                    }
                    if (path.endsWith(JS_MAP_EXT)) {
                        return this.rewriteSourceMap(data);
                    }
                    if (path.endsWith(DTS_EXT)) {
                        return this.rewriteDTSMappingURL(data);
                    }
                    if (path.endsWith(DTS_MAP_EXT)) {
                        return this.rewriteDTSMap(data);
                    }
                    return data;
                })();
                (0, debug_1.default)("Write file: %s", newPath);
                sys.writeFile(newPath, newData, writeByteOrderMark);
            },
            deleteFile: (path) => {
                const newPath = this.rewritePath(path);
                (0, debug_1.default)("Delete file: %s", newPath);
                sys.deleteFile?.(newPath);
            },
        };
    }
    createBuilder() {
        const buildOptions = {
            verbose: this.data.verbose,
            dry: this.data.dry,
            force: this.data.force,
        };
        const createProgram = this.ts.createSemanticDiagnosticsBuilderProgram;
        if (this.data.watch) {
            const host = this.ts.createSolutionBuilderWithWatchHost(this.system, createProgram, this.reporter.reportDiagnostic, this.reporter.reportSolutionBuilderStatus, this.reporter.reportWatchStatus);
            this.patchSolutionBuilderHost(host);
            return this.ts.createSolutionBuilderWithWatch(host, this.data.projects, buildOptions);
        }
        const host = this.ts.createSolutionBuilderHost(this.system, createProgram, this.reporter.reportDiagnostic, this.reporter.reportSolutionBuilderStatus, this.reporter.reportErrorSummary);
        this.patchSolutionBuilderHost(host);
        return this.ts.createSolutionBuilder(host, this.data.projects, buildOptions);
    }
    patchSolutionBuilderHost(host) {
        const { createProgram, reportDiagnostic } = host;
        const helpersNeeded = new Set();
        let resolvedShareHelpers;
        const transformers = {
            after: [
                (0, rewriteImport_1.createRewriteImportTransformer)({
                    extname: this.data.extname || JS_EXT,
                    getResolvedShareHelpers: () => resolvedShareHelpers,
                    helpersNeeded,
                    system: this.system,
                    ts: this.ts,
                }),
            ],
            afterDeclarations: [
                (0, rewriteImport_1.createRewriteImportTransformer)({
                    extname: this.data.extname || JS_EXT,
                    getResolvedShareHelpers: () => resolvedShareHelpers,
                    helpersNeeded,
                    system: this.system,
                    ts: this.ts,
                }),
            ],
        };
        const parseConfigFileHost = {
            ...this.system,
            onUnRecoverableConfigFileDiagnostic(diagnostic) {
                reportDiagnostic?.(diagnostic);
            },
        };
        host.getParsedCommandLine = (path) => {
            const basePath = (0, utils_1.trimSuffix)(path, (0, path_1.extname)(path));
            const { options } = this.ts.convertCompilerOptionsFromJson(this.data.target, (0, path_1.dirname)(path), path);
            const config = this.ts.getParsedCommandLineOfConfigFile(path, options, parseConfigFileHost);
            if (!config)
                return;
            if (this.data.shareHelpers) {
                const root = this.ts.getCommonSourceDirectoryOfConfig(config);
                config.options.importHelpers = true;
                resolvedShareHelpers = (0, path_1.resolve)(root, this.data.shareHelpers);
            }
            // Set separated tsbuildinfo paths to avoid that multiple workers to
            // access the same tsbuildinfo files and potentially read/write corrupted
            // tsbuildinfo files
            if (this.data.extname &&
                !config.options.tsBuildInfoFile &&
                (0, utils_1.isIncrementalCompilation)(config.options)) {
                config.options.tsBuildInfoFile = `${basePath}${this.data.extname}.tsbuildinfo`;
            }
            return config;
        };
        host.createProgram = (...args) => {
            const program = createProgram(...args);
            const emit = program.emit;
            program.emit = (targetSourceFile, writeFile, cancellationToken, emitOnlyDtsFiles, customTransformers) => {
                const result = emit(targetSourceFile, writeFile, cancellationToken, emitOnlyDtsFiles, (0, utils_1.mergeCustomTransformers)(customTransformers || {}, transformers));
                if (this.data.shareHelpers) {
                    const out = program.getCompilerOptions().outDir;
                    (0, assert_1.default)(out, "outDir must be set when specifying shareHelpers");
                    const write = writeFile || this.system.writeFile;
                    this.writeHelpers(helpersNeeded, write, out, program.getCompilerOptions());
                }
                return result;
            };
            return program;
        };
    }
    writeHelpers(helpersNeeded, write, out, compilerOptions) {
        (0, assert_1.default)(this.data.shareHelpers);
        const helperDeps = [...helpersNeeded].filter((e) => helpers_1.helpers[e]);
        let helperLength = 0;
        while (helperLength !== helperDeps.length) {
            helperLength = helperDeps.length;
            for (const helper of helperDeps) {
                for (const dep of helpers_1.helpers[helper]?.deps || []) {
                    if (!helperDeps.includes(dep)) {
                        helperDeps.unshift(dep);
                    }
                }
            }
        }
        write((0, path_1.resolve)(out, this.data.shareHelpers), this.ts.transpileModule(helperDeps.map((name) => helpers_1.helpers[name].code).join("\n\n") +
            `\n\nexport { ${[...helpersNeeded].join(", ")} };\n`, {
            compilerOptions: {
                module: compilerOptions.module,
            },
            fileName: "helpers.js",
            reportDiagnostics: false,
        }).outputText, false);
    }
    transpile() {
        for (const project of this.data.projects) {
            this.transpileProject(project);
        }
    }
    transpileProject(projectPath) {
        const tsConfigPath = this.system.fileExists(projectPath)
            ? projectPath
            : (0, path_1.join)(projectPath, "tsconfig.json");
        const { options } = this.ts.convertCompilerOptionsFromJson(this.data.target, projectPath, tsConfigPath);
        const parseConfigFileHost = {
            ...this.system,
            onUnRecoverableConfigFileDiagnostic: this.reporter.reportDiagnostic,
        };
        const config = this.ts.getParsedCommandLineOfConfigFile(tsConfigPath, options, parseConfigFileHost);
        if (!config)
            return;
        let resolvedShareHelpers;
        if (this.data.shareHelpers) {
            const root = this.ts.getCommonSourceDirectoryOfConfig(config);
            config.options.importHelpers = true;
            resolvedShareHelpers = (0, path_1.resolve)(root, this.data.shareHelpers);
        }
        const helpersNeeded = new Set();
        // TODO: Merge custom transformers
        const transformers = {
            after: [
                (0, rewriteImport_1.createRewriteImportTransformer)({
                    extname: this.data.extname || JS_EXT,
                    getResolvedShareHelpers: () => resolvedShareHelpers,
                    helpersNeeded,
                    system: this.system,
                    ts: this.ts,
                }),
            ],
        };
        for (const inputPath of config.fileNames) {
            // - Ignore if file does not exist
            // - or if file is a declaration file, which will generate an empty file and
            //   throw "Output generation failed" error
            if (!this.system.fileExists(inputPath) || inputPath.endsWith(".d.ts")) {
                continue;
            }
            const content = this.system.readFile(inputPath) || "";
            const [outputPath, sourceMapPath] = this.ts.getOutputFileNames(config, inputPath, false);
            const output = this.ts.transpileModule(content, {
                compilerOptions: config.options,
                fileName: inputPath,
                reportDiagnostics: true,
                transformers,
            });
            for (const diag of output.diagnostics ?? []) {
                this.reporter.reportDiagnostic(diag);
            }
            this.system.writeFile(outputPath, output.outputText);
            if (typeof output.sourceMapText === "string") {
                this.system.writeFile(sourceMapPath, output.sourceMapText);
            }
        }
        if (this.data.shareHelpers) {
            const out = config.options.outDir;
            (0, assert_1.default)(out, "outDir must be set when specifying shareHelpers");
            this.writeHelpers(helpersNeeded, this.system.writeFile, out, config.options);
        }
    }
}
exports.Worker = Worker;
//# sourceMappingURL=worker.js.map