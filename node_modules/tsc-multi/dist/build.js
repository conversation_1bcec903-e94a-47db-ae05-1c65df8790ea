"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.build = build;
const tslib_1 = require("tslib");
const child_process_1 = require("child_process");
const path_1 = require("path");
const string_to_stream_1 = tslib_1.__importDefault(require("string-to-stream"));
const utils_1 = require("./utils");
const report_1 = require("./report");
const signal_exit_1 = tslib_1.__importDefault(require("signal-exit"));
const p_all_1 = tslib_1.__importDefault(require("p-all"));
const debug_1 = tslib_1.__importDefault(require("./debug"));
const WORKER_PATH = (0, path_1.join)(__dirname, "worker/entry.js");
const DEFAULT_EXTNAME = ".js";
function validateTargets(targets) {
    const extnames = targets.map((target) => target.extname || DEFAULT_EXTNAME);
    const extMap = new Map();
    for (let i = 0; i < extnames.length; i++) {
        const ext = extnames[i];
        if (!ext.startsWith(".")) {
            throw new Error(`targets[${i}].extname must be started with ".".`);
        }
        const existedIndex = extMap.get(ext);
        if (existedIndex != null) {
            throw new Error(`targets[${i}].extname is already used in targets[${existedIndex}].extname`);
        }
        extMap.set(ext, i);
    }
}
async function runWorker({ stdout, stderr, ...options }) {
    const worker = (0, child_process_1.fork)(WORKER_PATH, [], {
        cwd: options.cwd,
        stdio: ["pipe", stdout, stderr, "ipc"],
    });
    if (worker.stdin) {
        (0, string_to_stream_1.default)(JSON.stringify(options)).pipe(worker.stdin);
    }
    const removeExitHandler = (0, signal_exit_1.default)((code, signal) => {
        (0, debug_1.default)(`Killing worker ${worker.pid} because parent process received ${signal || code || 0}`);
        worker.kill(code || "SIGTERM");
    });
    try {
        return await new Promise((resolve, reject) => {
            worker.on("error", reject);
            worker.on("exit", resolve);
        });
    }
    finally {
        removeExitHandler();
    }
}
async function build({ targets: inputTargets, stdout = "inherit", stderr = "inherit", projects, maxWorkers, ...options }) {
    if (!projects.length) {
        throw new Error("At least one project is required");
    }
    const targets = inputTargets && inputTargets.length ? inputTargets : [{}];
    validateTargets(targets);
    const reportStyles = (0, report_1.getReportStyles)();
    const codes = await (0, p_all_1.default)(targets.map(({ extname, transpileOnly, shareHelpers, ...target }, i) => {
        const prefix = `[${(0, utils_1.trimPrefix)(extname || DEFAULT_EXTNAME, ".")}]: `;
        const prefixStyle = reportStyles[i % reportStyles.length];
        return () => {
            return runWorker({
                ...options,
                projects,
                stdout,
                stderr,
                extname,
                shareHelpers,
                target,
                reportPrefix: prefixStyle(prefix),
                transpileOnly,
            });
        };
    }), { concurrency: maxWorkers });
    return codes.find((code) => code !== 0) || 0;
}
//# sourceMappingURL=build.js.map