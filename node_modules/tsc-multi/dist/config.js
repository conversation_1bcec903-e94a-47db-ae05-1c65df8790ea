"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.resolveProjectPath = resolveProjectPath;
exports.loadConfig = loadConfig;
const tslib_1 = require("tslib");
const path_1 = require("path");
const superstruct_1 = require("superstruct");
const debug_1 = tslib_1.__importDefault(require("./debug"));
const utils_1 = require("./utils");
const fast_glob_1 = tslib_1.__importDefault(require("fast-glob"));
const debug = debug_1.default.extend("config");
const targetSchema = (0, superstruct_1.type)({
    extname: (0, superstruct_1.optional)((0, superstruct_1.string)()),
    shareHelpers: (0, superstruct_1.optional)((0, superstruct_1.string)()),
    transpileOnly: (0, superstruct_1.optional)((0, superstruct_1.boolean)()),
});
const configSchema = (0, superstruct_1.object)({
    projects: (0, superstruct_1.optional)((0, superstruct_1.array)((0, superstruct_1.string)())),
    targets: (0, superstruct_1.optional)((0, superstruct_1.array)(targetSchema)),
    compiler: (0, superstruct_1.optional)((0, superstruct_1.string)()),
    maxWorkers: (0, superstruct_1.optional)((0, superstruct_1.min)((0, superstruct_1.integer)(), 1)),
});
async function resolveProjectPath(cwd, projects) {
    return (0, fast_glob_1.default)(projects, { cwd, onlyFiles: false });
}
async function loadConfig({ cwd = process.cwd(), path, }) {
    const mustLoadConfig = !!path;
    const configPath = (0, path_1.resolve)(cwd, path || "tsc-multi.json");
    debug("Read config from %s", configPath);
    const json = await (() => {
        if (mustLoadConfig)
            return (0, utils_1.readJSON)(configPath);
        return (0, utils_1.tryReadJSON)(configPath);
    })();
    const result = (0, superstruct_1.validate)(json, configSchema);
    if (result[0]) {
        throw result[0];
    }
    const config = result[1];
    return {
        ...config,
        cwd,
        projects: await resolveProjectPath((0, path_1.dirname)(configPath), config.projects || []),
    };
}
//# sourceMappingURL=config.js.map