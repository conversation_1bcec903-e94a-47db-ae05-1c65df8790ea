import { Config } from "./config";
import { Stream } from "stream";
type Stdio = "ignore" | "inherit" | Stream;
export interface BuildOptions extends Config {
    watch?: boolean;
    clean?: boolean;
    verbose?: boolean;
    dry?: boolean;
    force?: boolean;
    stdout?: Stdio;
    stderr?: Stdio;
    maxWorkers?: number;
}
export declare function build({ targets: inputTargets, stdout, stderr, projects, maxWorkers, ...options }: BuildOptions): Promise<number>;
export {};
