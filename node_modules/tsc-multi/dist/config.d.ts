import { Infer } from "superstruct";
declare const targetSchema: import("superstruct").Struct<{
    extname?: string | undefined;
    shareHelpers?: string | undefined;
    transpileOnly?: boolean | undefined;
}, {
    extname: import("superstruct").Struct<string | undefined, null>;
    shareHelpers: import("superstruct").Struct<string | undefined, null>;
    transpileOnly: import("superstruct").Struct<boolean | undefined, null>;
}>;
export type Target = Infer<typeof targetSchema> & {
    [key: string]: unknown;
};
declare const configSchema: import("superstruct").Struct<{
    projects?: string[] | undefined;
    targets?: {
        extname?: string | undefined;
        shareHelpers?: string | undefined;
        transpileOnly?: boolean | undefined;
    }[] | undefined;
    compiler?: string | undefined;
    maxWorkers?: number | undefined;
}, {
    projects: import("superstruct").Struct<string[] | undefined, import("superstruct").Struct<string, null>>;
    targets: import("superstruct").Struct<{
        extname?: string | undefined;
        shareHelpers?: string | undefined;
        transpileOnly?: boolean | undefined;
    }[] | undefined, import("superstruct").Struct<{
        extname?: string | undefined;
        shareHelpers?: string | undefined;
        transpileOnly?: boolean | undefined;
    }, {
        extname: import("superstruct").Struct<string | undefined, null>;
        shareHelpers: import("superstruct").Struct<string | undefined, null>;
        transpileOnly: import("superstruct").Struct<boolean | undefined, null>;
    }>>;
    compiler: import("superstruct").Struct<string | undefined, null>;
    maxWorkers: import("superstruct").Struct<number | undefined, null>;
}>;
export type InferConfig = Infer<typeof configSchema>;
export type Config = InferConfig & {
    cwd: string;
    projects: string[];
    targets?: Target[];
};
export declare function resolveProjectPath(cwd: string, projects: string[]): Promise<string[]>;
export interface LoadConfigOptions {
    cwd?: string;
    path?: string;
}
export declare function loadConfig({ cwd, path, }: LoadConfigOptions): Promise<Config>;
export {};
