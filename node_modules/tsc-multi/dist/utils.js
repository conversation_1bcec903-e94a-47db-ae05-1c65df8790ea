"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.trimPrefix = trimPrefix;
exports.trimSuffix = trimSuffix;
exports.readJSON = readJSON;
exports.tryReadJSON = tryReadJSON;
exports.mergeCustomTransformers = mergeCustomTransformers;
exports.isIncrementalCompilation = isIncrementalCompilation;
const promises_1 = require("fs/promises");
function trimPrefix(input, prefix) {
    if (input.startsWith(prefix)) {
        return input.substring(prefix.length);
    }
    return input;
}
function trimSuffix(input, suffix) {
    if (input.endsWith(suffix)) {
        return input.substring(0, input.length - suffix.length);
    }
    return input;
}
async function readJSON(path) {
    const content = await (0, promises_1.readFile)(path, "utf8");
    return JSON.parse(content);
}
async function tryReadJSON(path) {
    try {
        return await readJSO<PERSON>(path);
    }
    catch (err) {
        if (err.code === "ENOENT")
            return {};
        throw err;
    }
}
function mergeArray(target = [], source = []) {
    return [...target, ...source];
}
function mergeCustomTransformers(target, source) {
    return {
        before: mergeArray(target.before, source.before),
        after: mergeArray(target.after, source.after),
        afterDeclarations: mergeArray(target.afterDeclarations, source.afterDeclarations),
    };
}
function isIncrementalCompilation(options) {
    return !!(options.incremental || options.composite);
}
//# sourceMappingURL=utils.js.map