import type { Formatter } from "picocolors/types";
import { Writable } from "stream";
import type ts from "typescript";
export interface Reporter {
    formatDiagnosticsHost: ts.FormatDiagnosticsHost;
    reportDiagnostic: ts.DiagnosticReporter;
    reportSolutionBuilderStatus: ts.DiagnosticReporter;
    reportErrorSummary: ts.ReportEmitErrorSummary;
    reportWatchStatus: ts.WatchStatusReporter;
}
export interface ReporterOptions {
    cwd: string;
    system: ts.System;
    formatDiagnostics: (typeof ts)["formatDiagnostics"];
    output: Writable;
    prefix?: string;
}
export declare function createReporter({ cwd, system, formatDiagnostics, output, prefix, }: ReporterOptions): Reporter;
export declare function getReportStyles(): Formatter[];
