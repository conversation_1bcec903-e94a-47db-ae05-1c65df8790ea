{"version": 3, "file": "cli.js", "sourceRoot": "", "sources": ["../src/cli.ts"], "names": [], "mappings": ";;;AAAA,gEAAgC;AAChC,mCAAgC;AAChC,qCAA0D;AAE1D,IAAA,eAAK,EAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;KACzB,OAAO,CAAC;IACP,KAAK,EAAE;QACL,IAAI,EAAE,SAAS;QACf,KAAK,EAAE,GAAG;QACV,WAAW,EAAE,sDAAsD;KACpE;IACD,KAAK,EAAE;QACL,IAAI,EAAE,SAAS;QACf,WAAW,EAAE,yDAAyD;KACvE;IACD,OAAO,EAAE;QACP,IAAI,EAAE,SAAS;QACf,WAAW,EAAE,mBAAmB;KACjC;IACD,GAAG,EAAE;QACH,IAAI,EAAE,SAAS;QACf,WAAW,EACT,8DAA8D;KACjE;IACD,KAAK,EAAE;QACL,IAAI,EAAE,SAAS;QACf,WAAW,EAAE,uBAAuB;KACrC;IACD,GAAG,EAAE;QACH,IAAI,EAAE,QAAQ;QACd,WAAW,EAAE,4BAA4B;KAC1C;IACD,MAAM,EAAE;QACN,IAAI,EAAE,QAAQ;QACd,WAAW,EAAE,0DAA0D;KACxE;IACD,QAAQ,EAAE;QACR,IAAI,EAAE,QAAQ;QACd,WAAW,EAAE,mCAAmC;KACjD;IACD,UAAU,EAAE;QACV,IAAI,EAAE,QAAQ;QACd,WAAW,EAAE,mDAAmD;KACjE;CACF,CAAC;KACD,OAAO,CACN,iBAAiB,EACjB,yDAAyD,EACzD,CAAC,GAAG,EAAE,EAAE;IACN,OAAO,GAAG;SACP,UAAU,CAAC,UAAU,EAAE;QACtB,KAAK,EAAE,IAAI;QACX,IAAI,EAAE,QAAQ;QACd,WAAW,EACT,sEAAsE;KACzE,CAAC;SACD,OAAO,CAAC;QACP,CAAC,IAAI,EAAE,uBAAuB,CAAC;QAC/B,CAAC,YAAY,EAAE,uCAAuC,CAAC;QACvD,CAAC,YAAY,EAAE,qBAAqB,CAAC;QACrC,CAAC,yBAAyB,EAAE,qBAAqB,CAAC;QAClD,CAAC,oBAAoB,EAAE,0BAA0B,CAAC;KACnD,CAAC,CAAC;AACP,CAAC,EACD,KAAK,EAAE,IAAI,EAAE,EAAE;IACb,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,EAAE,CAAC;IACrC,MAAM,MAAM,GAAG,MAAM,IAAA,mBAAU,EAAC;QAC9B,GAAG,EAAE,IAAI,CAAC,GAAG;QACb,IAAI,EAAE,IAAI,CAAC,MAAM;KAClB,CAAC,CAAC;IACH,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;QACpB,MAAM,CAAC,QAAQ,GAAG,MAAM,IAAA,2BAAkB,EAAC,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;IACnE,CAAC;IACD,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;QAC5B,MAAM,CAAC,QAAQ,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IACjC,CAAC;IACD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;QAClB,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;IAClC,CAAC;IAED,OAAO,CAAC,QAAQ,GAAG,MAAM,IAAA,aAAK,EAAC;QAC7B,GAAG,MAAM;QACT,OAAO,EAAE,IAAI,CAAC,OAAO;QACrB,GAAG,EAAE,IAAI,CAAC,GAAG;QACb,KAAK,EAAE,IAAI,CAAC,KAAK;QACjB,KAAK,EAAE,IAAI,CAAC,KAAK;QACjB,KAAK,EAAE,IAAI,CAAC,KAAK;QACjB,UAAU,EAAE,IAAI,CAAC,UAAU;KAC5B,CAAC,CAAC;AACL,CAAC,CACF;KACA,cAAc,CAAC,KAAK,CAAC;KACrB,KAAK,EAAE,CAAC"}