import ts from "typescript";
export declare function trimPrefix(input: string, prefix: string): string;
export declare function trimSuffix(input: string, suffix: string): string;
export declare function readJSON(path: string): Promise<any>;
export declare function tryReadJSON(path: string): Promise<any>;
export declare function mergeCustomTransformers(target: ts.CustomTransformers, source: ts.CustomTransformers): ts.CustomTransformers;
export declare function isIncrementalCompilation(options: ts.CompilerOptions): boolean;
