"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createReporter = createReporter;
exports.getReportStyles = getReportStyles;
const tslib_1 = require("tslib");
const picocolors_1 = tslib_1.__importDefault(require("picocolors"));
function createReporter({ cwd, system, formatDiagnostics, output, prefix = "", }) {
    const formatDiagnosticsHost = {
        getCurrentDirectory: () => cwd,
        getCanonicalFileName: (fileName) => fileName,
        getNewLine: () => system.newLine,
    };
    function writeString(content) {
        output.write(prefix + content);
    }
    function reportDiagnostic(diagnostic) {
        const formatted = formatDiagnostics([diagnostic], formatDiagnosticsHost);
        writeString(formatted);
    }
    function reportErrorSummary(errorCount) {
        writeString(`Found ${errorCount} ${errorCount === 1 ? "error" : "errors"}.\n`);
    }
    function reportWatchStatus(diagnostic, newLine) {
        const formatted = formatDiagnostics([diagnostic], {
            ...formatDiagnosticsHost,
            getNewLine: () => newLine,
        });
        writeString(formatted);
    }
    return {
        formatDiagnosticsHost,
        reportDiagnostic,
        reportSolutionBuilderStatus: reportDiagnostic,
        reportErrorSummary,
        reportWatchStatus,
    };
}
function getReportStyles() {
    return [picocolors_1.default.red, picocolors_1.default.green, picocolors_1.default.yellow, picocolors_1.default.blue, picocolors_1.default.magenta, picocolors_1.default.cyan];
}
//# sourceMappingURL=report.js.map