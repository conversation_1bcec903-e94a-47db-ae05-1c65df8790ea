{"name": "tsc-multi", "version": "1.1.4", "description": "Compile multiple TypeScript projects into multiple targets.", "main": "dist/index.js", "types": "dist/index.d.ts", "bin": {"tsc-multi": "bin/tsc-multi.js"}, "repository": {"type": "git", "url": "git+https://github.com/tommy351/tsc-multi.git"}, "keywords": ["typescript", "tsc", "ts", "compiler"], "files": ["bin", "dist"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/tommy351/tsc-multi/issues"}, "homepage": "https://github.com/tommy351/tsc-multi#readme", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts,jsx,tsx}": "eslint --fix"}, "engines": {"node": ">=14"}, "dependencies": {"debug": "^4.3.7", "fast-glob": "^3.3.2", "get-stdin": "^8.0.0", "p-all": "^3.0.0", "picocolors": "^1.1.1", "signal-exit": "^3.0.7", "string-to-stream": "^3.0.1", "superstruct": "^1.0.4", "tslib": "^2.8.1", "yargs": "^17.7.2"}, "devDependencies": {"@tsconfig/node10": "^1.0.11", "@tsconfig/node14": "^1.0.3", "@types/debug": "^4.1.12", "@types/fs-extra": "^11.0.4", "@types/jest": "^29.5.14", "@types/node": "^18.19.64", "@types/signal-exit": "^3.0.4", "@types/tmp": "^0.2.6", "@types/yargs": "^17.0.33", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "esbuild": "^0.10.0", "eslint": "^8.57.1", "eslint-config-prettier": "^8.10.0", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.37.2", "execa": "^5.1.1", "fs-extra": "^11.2.0", "husky": "^4.3.8", "jest": "^29.7.0", "jest-extended": "^3.2.4", "jest-file-snapshot": "^0.5.0", "lint-staged": "^10.5.4", "npm-run-all": "^4.1.5", "prettier": "^2.8.8", "tmp-promise": "^3.0.3", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "ts-transformer-keys": "^0.4.4", "ttsc": "^0.3.1", "typescript": "^5.6.3"}, "peerDependencies": {"typescript": ">=4.3.0"}, "scripts": {"build": "node scripts/tslib.cjs && tsc -P tsconfig.build.json", "watch": "npm run build -- --watch", "lint": "eslint . --ext .js,.ts,.jsx,.tsx", "clean": "rm -rf dist", "test": "npm-run-all test:*", "test:integration": "jest --runInBand --config integration/jest.config.js"}}