"""
Test environment configuration for AdMesh Protocol
Uses production Firebase/database but exposes on localhost for testing
"""

import os
from typing import Dict, Any
from .base import BaseConfig


class TestConfig(BaseConfig):
    """Configuration for test environment - production data with local API"""

    @property
    def firebase_config(self) -> Dict[str, Any]:
        """Firebase configuration for test (uses production)"""
        return {
            "project_id": "admesh-9560c",
            "api_key": "AIzaSyDC7cun2Klpj_NdyGGAJz0FGezjeS7dtQM",
            "auth_domain": "admesh-9560c.firebaseapp.com",
            "storage_bucket": "admesh-9560c.firebasestorage.app",
            "messaging_sender_id": "857884660055",
            "app_id": "1:857884660055:web:4f7cbe97e3e62b794bee12"
        }

    @property
    def api_base_url(self) -> str:
        """API base URL for test (localhost)"""
        return os.getenv("API_BASE_URL", "http://127.0.0.1:8000")

    @property
    def frontend_url(self) -> str:
        """Frontend URL for test"""
        return os.getenv("FRONTEND_URL", "http://localhost:3000")

    @property
    def cors_origins(self) -> list:
        """CORS origins for test environment"""
        return [
            "http://localhost:3000",
            "http://127.0.0.1:3000",
            "http://localhost:3001",
            "http://127.0.0.1:3001",
            "https://www.useadmesh.com",
            "https://useadmesh.com",
            "https://beta.useadmesh.com"
        ]

    @property
    def database_config(self) -> Dict[str, Any]:
        """Database configuration for test (uses production database)"""
        return {
            "type": "firestore",
            "project_id": "admesh-9560c",
            "credentials_path": self.get_firebase_credentials_path(),
            "emulator": False,
            "timeout": 30
        }

    @property
    def cache_config(self) -> Dict[str, Any]:
        """Cache configuration for test"""
        return {
            "type": "memory",
            "ttl": 300,  # 5 minutes
            "max_size": 1000
        }

    @property
    def rate_limiting(self) -> Dict[str, Any]:
        """Rate limiting configuration for test"""
        return {
            "enabled": True,
            "requests_per_minute": 1000,  # Higher limit for testing
            "burst_size": 100
        }

    @property
    def external_services(self) -> Dict[str, Any]:
        """External services configuration for test"""
        return {
            "openrouter": {
                "api_key": os.getenv("OPENROUTER_API_KEY"),
                "base_url": "https://openrouter.ai/api/v1",
                "timeout": 30
            },
            "resend": {
                "api_key": os.getenv("RESEND_API_KEY"),
                "from_email": "<EMAIL>"
            }
        }

    @property
    def security_config(self) -> Dict[str, Any]:
        """Security configuration for test"""
        return {
            "jwt_secret": os.getenv("JWT_SECRET", "test-jwt-secret-key"),
            "jwt_algorithm": "HS256",
            "jwt_expiration": 3600,  # 1 hour
            "api_key_prefix": "sk_test_",
            "require_https": False,  # Allow HTTP for local testing
            "cors_credentials": True
        }

    @property
    def monitoring_config(self) -> Dict[str, Any]:
        """Monitoring configuration for test"""
        return {
            "sentry_dsn": os.getenv("SENTRY_DSN"),
            "enable_metrics": True,
            "enable_tracing": True,
            "sample_rate": 1.0  # 100% sampling for testing
        }

    @property
    def performance_config(self) -> Dict[str, Any]:
        """Performance configuration for test"""
        return {
            "max_workers": 4,
            "request_timeout": 30,
            "keep_alive_timeout": 5,
            "max_request_size": 10 * 1024 * 1024,  # 10MB
            "enable_compression": True
        }

    def get_firebase_credentials_path(self) -> str:
        """Get the path to Firebase credentials for test (uses production)"""
        credentials_path = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
        if credentials_path:
            return credentials_path

        # Use production credentials for test environment
        return os.path.join(os.path.dirname(__file__), "..", "firebase", "serviceAccountKey.json")

    def get_required_env_vars(self) -> list:
        """Get list of required environment variables for test"""
        return ["OPENROUTER_API_KEY", "RESEND_API_KEY"]

    def validate_config(self) -> None:
        """Validate test configuration"""
        super().validate_config()

        # Ensure production Firebase credentials exist
        credentials_path = self.get_firebase_credentials_path()
        if not os.path.exists(credentials_path):
            raise ValueError(f"Production Firebase credentials not found at: {credentials_path}")

        # Validate required environment variables
        required_vars = self.get_required_env_vars()
        missing_vars = [var for var in required_vars if not os.getenv(var)]
        if missing_vars:
            raise ValueError(f"Missing required environment variables for test: {missing_vars}")
